from pydantic import BaseModel, EmailStr, Field
from typing import List, Optional
from datetime import date, datetime

# Base schemas that were in schemas.py
class UserManagementUser(BaseModel):
    id: int
    first_name: str
    last_name: str
    email: str
    subscription_plan: Optional[str]
    subscription_active: Optional[bool]
    subscription_end_date: Optional[date]
    is_active: bool

class UserManagementResponse(BaseModel):
    users: List[UserManagementUser]

class UserResume(BaseModel):
    id: int
    version_name: Optional[str]
    is_primary: bool
    created_at: datetime

class UserJobApplication(BaseModel):
    id: int
    job_title: str
    company_name: str
    application_date: date
    status: str

class UserAIUsage(BaseModel):
    id: int
    action_type: str
    usage_count: int
    created_at: datetime

class UserDetailResponse(BaseModel):
    id: int
    first_name: str
    last_name: str
    email: str
    subscription_plan: Optional[str]
    subscription_active: Optional[bool]
    subscription_end_date: Optional[date]
    is_active: bool
    total_job_applications: int
    total_resumes: int
    total_ai_usage: int
    job_applications: List[UserJobApplication]
    resumes: List[UserResume]
    ai_usage_logs: List[UserAIUsage]

class PlanCreate(BaseModel):
    name: str
    price: str
    currency: str
    application_limit: Optional[int]
    customization_limit: Optional[int]

class PlanUpdate(BaseModel):
    name: Optional[str]
    price: Optional[str]
    currency: Optional[str]
    application_limit: Optional[int]
    customization_limit: Optional[int]
    is_active: Optional[bool]

class PlanResponse(BaseModel):
    id: int
    name: str
    price: str
    currency: str
    application_limit: Optional[int]
    customization_limit: Optional[int]
    is_active: bool
    created_at: datetime

class PlansResponse(BaseModel):
    plans: List[PlanResponse]

class PaymentResponse(BaseModel):
    id: int
    user_id: int
    user_name: str
    user_email: str
    transaction_id: Optional[str]
    amount: str
    currency: str
    status: str
    payment_method: Optional[str]
    plan_name: Optional[str]
    created_at: datetime

class PaymentsResponse(BaseModel):
    payments: List[PaymentResponse]
    total_count: int

# Admin Authentication Schemas
class AdminCreate(BaseModel):
    email: EmailStr
    password: str
    first_name: str
    last_name: str

class AdminResponse(BaseModel):
    id: int
    email: EmailStr
    first_name: str
    last_name: str
    created_at: datetime

class AdminsResponse(BaseModel):
    admins: List[AdminResponse]

# Admin Dashboard Schemas
class AdminDashboardResponse(BaseModel):
    total_users: int
    active_users: int
    inactive_users: int
    total_job_applications: int
    total_revenue: float
    total_plans: int
    active_subscriptions: int
    recent_payments: List[PaymentResponse]
    recent_users: List[UserManagementUser]

# Admin Analytics Schemas
class UserActivityResponse(BaseModel):
    total_logins: int
    average_logins_per_user: float
    most_active_day: str
    least_active_day: str

class RevenueAnalyticsResponse(BaseModel):
    total_revenue: float
    average_revenue_per_user: float
    most_popular_plan: str
    highest_revenue_day: str

class AdminStats(BaseModel):
    total_admins: int
    total_customers: int

# Admin User Management Schemas
class UserRoleUpdate(BaseModel):
    role: str = Field(..., description="User role (admin or customer)")

class UserStatusUpdate(BaseModel):
    is_active: bool = Field(..., description="User active status")

# Admin Subscription Management Schemas
class SubscriptionStatsResponse(BaseModel):
    total_subscriptions: int
    active_subscriptions: int
    expired_subscriptions: int
    revenue_by_plan: dict
    subscriptions_by_plan: dict

# Admin Payment Management Schemas
class PaymentStatsResponse(BaseModel):
    total_payments: int
    total_revenue: float
    average_payment: float
    payment_by_status: dict
    payment_by_method: dict