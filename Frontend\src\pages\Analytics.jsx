import React, { useState, useEffect } from 'react';
import { BarChart3, TrendingUp, Calendar, Filter, AlertCircle } from 'lucide-react';
import axios from 'axios';

const Analytics = () => {
  const [analyticsData, setAnalyticsData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [dateRange, setDateRange] = useState({
    start_date: '',
    end_date: ''
  });
  const [showFilters, setShowFilters] = useState(false);

  useEffect(() => {
    fetchAnalyticsData();
  }, []);

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);
      
      // Build query parameters
      const params = {};
      if (dateRange.start_date) params.start_date = dateRange.start_date;
      if (dateRange.end_date) params.end_date = dateRange.end_date;
      
      // Fetch user activity data
      const userActivityResponse = await axios.get('/admin/analytics/user-activity', { params });
      
      // Fetch revenue data
      const revenueResponse = await axios.get('/admin/analytics/revenue', { params });
      
      setAnalyticsData({
        userActivity: userActivityResponse.data,
        revenue: revenueResponse.data
      });
    } catch (error) {
      console.error('Error fetching analytics data:', error);
      setError('Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  };

  const handleDateChange = (e) => {
    const { name, value } = e.target;
    setDateRange(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const applyFilters = (e) => {
    e.preventDefault();
    fetchAnalyticsData();
  };

  const resetFilters = () => {
    setDateRange({
      start_date: '',
      end_date: ''
    });
    fetchAnalyticsData();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">Analytics Dashboard</h1>
        <div className="flex space-x-2">
          <button 
            onClick={() => setShowFilters(!showFilters)}
            className="btn-secondary flex items-center"
          >
            <Filter className="h-5 w-5 mr-2" />
            {showFilters ? 'Hide Filters' : 'Show Filters'}
          </button>
          <button 
            onClick={fetchAnalyticsData}
            className="btn-secondary"
          >
            Refresh
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <AlertCircle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Date Range Filters */}
      {showFilters && (
        <div className="bg-white shadow-sm rounded-lg p-6 border border-gray-200">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Filter Analytics</h2>
          <form onSubmit={applyFilters} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="start_date" className="block text-sm font-medium text-gray-700">Start Date</label>
                <div className="mt-1 relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Calendar className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="date"
                    id="start_date"
                    name="start_date"
                    className="input-field pl-10"
                    value={dateRange.start_date}
                    onChange={handleDateChange}
                  />
                </div>
              </div>
              
              <div>
                <label htmlFor="end_date" className="block text-sm font-medium text-gray-700">End Date</label>
                <div className="mt-1 relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Calendar className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="date"
                    id="end_date"
                    name="end_date"
                    className="input-field pl-10"
                    value={dateRange.end_date}
                    onChange={handleDateChange}
                  />
                </div>
              </div>
            </div>
            
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={resetFilters}
                className="btn-secondary"
              >
                Reset
              </button>
              <button
                type="submit"
                className="btn-primary"
              >
                Apply Filters
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Analytics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* User Activity Analytics */}
        <div className="card">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-lg font-semibold text-gray-900">User Activity</h2>
            <div className="p-2 rounded-full bg-blue-100">
              <Users className="w-5 h-5 text-blue-600" />
            </div>
          </div>
          
          {analyticsData?.userActivity ? (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-sm text-gray-500">New Users</p>
                  <p className="text-2xl font-bold text-gray-900">{analyticsData.userActivity.new_users || 0}</p>
                </div>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-sm text-gray-500">Active Users</p>
                  <p className="text-2xl font-bold text-gray-900">{analyticsData.userActivity.active_users || 0}</p>
                </div>
              </div>
              
              <div>
                <h3 className="text-md font-medium text-gray-700 mb-2">Activity Breakdown</h3>
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Resume Creations</span>
                    <span className="text-sm font-medium">{analyticsData.userActivity.resume_creations || 0}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div 
                      className="bg-blue-600 h-2.5 rounded-full" 
                      style={{ width: `${Math.min(100, (analyticsData.userActivity.resume_creations / (analyticsData.userActivity.total_actions || 1)) * 100)}%` }}
                    ></div>
                  </div>
                </div>
                
                <div className="space-y-2 mt-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Job Applications</span>
                    <span className="text-sm font-medium">{analyticsData.userActivity.job_applications || 0}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div 
                      className="bg-purple-600 h-2.5 rounded-full" 
                      style={{ width: `${Math.min(100, (analyticsData.userActivity.job_applications / (analyticsData.userActivity.total_actions || 1)) * 100)}%` }}
                    ></div>
                  </div>
                </div>
                
                <div className="space-y-2 mt-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">AI Interactions</span>
                    <span className="text-sm font-medium">{analyticsData.userActivity.ai_interactions || 0}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div 
                      className="bg-green-600 h-2.5 rounded-full" 
                      style={{ width: `${Math.min(100, (analyticsData.userActivity.ai_interactions / (analyticsData.userActivity.total_actions || 1)) * 100)}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <p className="text-gray-500 text-center py-4">No user activity data available</p>
          )}
        </div>

        {/* Revenue Analytics */}
        <div className="card">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-lg font-semibold text-gray-900">Revenue Analytics</h2>
            <div className="p-2 rounded-full bg-green-100">
              <TrendingUp className="w-5 h-5 text-green-600" />
            </div>
          </div>
          
          {analyticsData?.revenue ? (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-sm text-gray-500">Total Revenue</p>
                  <p className="text-2xl font-bold text-gray-900">
                    ${analyticsData.revenue.total_revenue?.toFixed(2) || '0.00'}
                  </p>
                </div>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-sm text-gray-500">New Subscriptions</p>
                  <p className="text-2xl font-bold text-gray-900">{analyticsData.revenue.new_subscriptions || 0}</p>
                </div>
              </div>
              
              <div>
                <h3 className="text-md font-medium text-gray-700 mb-2">Revenue by Plan</h3>
                {analyticsData.revenue.revenue_by_plan && Object.entries(analyticsData.revenue.revenue_by_plan).length > 0 ? (
                  <div className="space-y-3">
                    {Object.entries(analyticsData.revenue.revenue_by_plan).map(([plan, amount]) => (
                      <div key={plan} className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-600">{plan}</span>
                          <span className="text-sm font-medium">${parseFloat(amount).toFixed(2)}</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2.5">
                          <div 
                            className="bg-green-600 h-2.5 rounded-full" 
                            style={{ width: `${Math.min(100, (amount / (analyticsData.revenue.total_revenue || 1)) * 100)}%` }}
                          ></div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-center py-2">No plan revenue data available</p>
                )}
              </div>
              
              <div>
                <h3 className="text-md font-medium text-gray-700 mb-2">Monthly Trend</h3>
                {analyticsData.revenue.monthly_revenue && analyticsData.revenue.monthly_revenue.length > 0 ? (
                  <div className="h-40 flex items-end justify-between space-x-2">
                    {analyticsData.revenue.monthly_revenue.map((item, index) => {
                      const maxRevenue = Math.max(...analyticsData.revenue.monthly_revenue.map(i => i.amount));
                      const height = maxRevenue > 0 ? (item.amount / maxRevenue) * 100 : 0;
                      
                      return (
                        <div key={index} className="flex flex-col items-center flex-1">
                          <div 
                            className="w-full bg-red-500 rounded-t-sm" 
                            style={{ height: `${height}%` }}
                          ></div>
                          <span className="text-xs text-gray-500 mt-1">{item.month}</span>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <p className="text-gray-500 text-center py-2">No monthly trend data available</p>
                )}
              </div>
            </div>
          ) : (
            <p className="text-gray-500 text-center py-4">No revenue data available</p>
          )}
        </div>
      </div>

      {/* Additional Analytics */}
      <div className="card">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Performance Metrics</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="space-y-2">
            <h3 className="text-md font-medium text-gray-700">Conversion Rate</h3>
            <p className="text-2xl font-bold text-gray-900">
              {analyticsData?.userActivity?.conversion_rate ? 
                `${(analyticsData.userActivity.conversion_rate * 100).toFixed(1)}%` : 
                '0%'}
            </p>
            <p className="text-sm text-gray-500">Free to paid conversion</p>
          </div>
          
          <div className="space-y-2">
            <h3 className="text-md font-medium text-gray-700">Average Revenue Per User</h3>
            <p className="text-2xl font-bold text-gray-900">
              ${analyticsData?.revenue?.arpu ? 
                analyticsData.revenue.arpu.toFixed(2) : 
                '0.00'}
            </p>
            <p className="text-sm text-gray-500">ARPU</p>
          </div>
          
          <div className="space-y-2">
            <h3 className="text-md font-medium text-gray-700">Churn Rate</h3>
            <p className="text-2xl font-bold text-gray-900">
              {analyticsData?.userActivity?.churn_rate ? 
                `${(analyticsData.userActivity.churn_rate * 100).toFixed(1)}%` : 
                '0%'}
            </p>
            <p className="text-sm text-gray-500">Subscription cancellations</p>
          </div>
        </div>
      </div>
    </div>
  );
};

// Missing Users component
const Users = ({ className }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" />
      <circle cx="9" cy="7" r="4" />
      <path d="M23 21v-2a4 4 0 0 0-3-3.87" />
      <path d="M16 3.13a4 4 0 0 1 0 7.75" />
    </svg>
  );
};

export default Analytics;