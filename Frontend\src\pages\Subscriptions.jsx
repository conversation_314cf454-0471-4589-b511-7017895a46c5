import React, { useState, useEffect } from 'react';
import { CreditCard, Plus, Edit, Trash, AlertCircle } from 'lucide-react';
import axios from 'axios';

const Subscriptions = () => {
  const [plans, setPlans] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [editingPlan, setEditingPlan] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    price: '',
    currency: 'USD',
    application_limit: '',
    customization_limit: '',
    is_active: true
  });

  useEffect(() => {
    fetchPlans();
  }, []);

  const fetchPlans = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/admin/subscriptions');
      setPlans(response.data.plans);
    } catch (error) {
      console.error('Error fetching subscription plans:', error);
      setError('Failed to load subscription plans');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  const openCreateModal = () => {
    setEditingPlan(null);
    setFormData({
      name: '',
      price: '',
      currency: 'USD',
      application_limit: '',
      customization_limit: '',
      is_active: true
    });
    setShowModal(true);
  };

  const openEditModal = (plan) => {
    setEditingPlan(plan);
    setFormData({
      name: plan.name,
      price: plan.price,
      currency: plan.currency,
      application_limit: plan.application_limit || '',
      customization_limit: plan.customization_limit || '',
      is_active: plan.is_active
    });
    setShowModal(true);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      // Convert string inputs to appropriate types
      const payload = {
        ...formData,
        application_limit: formData.application_limit ? parseInt(formData.application_limit) : null,
        customization_limit: formData.customization_limit ? parseInt(formData.customization_limit) : null
      };

      if (editingPlan) {
        // Update existing plan
        await axios.put(`/admin/subscriptions/${editingPlan.id}`, payload);
      } else {
        // Create new plan
        await axios.post('/admin/subscriptions', payload);
      }

      setShowModal(false);
      fetchPlans();
    } catch (error) {
      console.error('Error saving subscription plan:', error);
      setError(error.response?.data?.detail || 'Failed to save subscription plan');
    }
  };

  const handleDelete = async (planId) => {
    if (window.confirm('Are you sure you want to delete this subscription plan? This action cannot be undone.')) {
      try {
        await axios.delete(`/admin/subscriptions/${planId}`);
        fetchPlans();
      } catch (error) {
        console.error('Error deleting subscription plan:', error);
        setError(error.response?.data?.detail || 'Failed to delete subscription plan');
      }
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">Subscription Plans</h1>
        <button 
          onClick={openCreateModal}
          className="btn-primary flex items-center"
        >
          <Plus className="h-5 w-5 mr-2" />
          Add New Plan
        </button>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <AlertCircle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Plans Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {plans.length > 0 ? (
          plans.map((plan) => (
            <div key={plan.id} className={`card border-l-4 ${plan.is_active ? 'border-l-green-500' : 'border-l-gray-300'}`}>
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{plan.name}</h3>
                  <p className="text-2xl font-bold text-gray-900 mt-2">
                    {plan.currency === 'USD' ? '$' : plan.currency}{plan.price}
                  </p>
                </div>
                <div className="p-3 rounded-full bg-blue-100">
                  <CreditCard className="w-6 h-6 text-blue-600" />
                </div>
              </div>
              
              <div className="mt-4 space-y-2">
                {plan.application_limit && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Application Limit:</span>
                    <span className="text-sm font-medium">{plan.application_limit}</span>
                  </div>
                )}
                {plan.customization_limit && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Customization Limit:</span>
                    <span className="text-sm font-medium">{plan.customization_limit}</span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Status:</span>
                  <span className={`text-sm font-medium ${plan.is_active ? 'text-green-600' : 'text-gray-500'}`}>
                    {plan.is_active ? 'Active' : 'Inactive'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Created:</span>
                  <span className="text-sm">{new Date(plan.created_at).toLocaleDateString()}</span>
                </div>
              </div>
              
              <div className="mt-6 flex justify-end space-x-2">
                <button 
                  onClick={() => openEditModal(plan)}
                  className="p-2 text-blue-600 hover:bg-blue-50 rounded-full"
                >
                  <Edit className="h-5 w-5" />
                </button>
                <button 
                  onClick={() => handleDelete(plan.id)}
                  className="p-2 text-red-600 hover:bg-red-50 rounded-full"
                >
                  <Trash className="h-5 w-5" />
                </button>
              </div>
            </div>
          ))
        ) : (
          <div className="col-span-full text-center py-12 bg-gray-50 rounded-lg border border-gray-200">
            <CreditCard className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No subscription plans</h3>
            <p className="mt-1 text-sm text-gray-500">Get started by creating a new subscription plan.</p>
            <div className="mt-6">
              <button
                onClick={openCreateModal}
                className="btn-primary"
              >
                <Plus className="h-5 w-5 mr-2 inline" />
                New Plan
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center">
          <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">
                  {editingPlan ? 'Edit Subscription Plan' : 'Create Subscription Plan'}
                </h3>
                <button 
                  onClick={() => setShowModal(false)}
                  className="text-gray-400 hover:text-gray-500"
                >
                  <span className="sr-only">Close</span>
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700">Plan Name</label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    required
                    className="input-field mt-1"
                    value={formData.name}
                    onChange={handleInputChange}
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="price" className="block text-sm font-medium text-gray-700">Price</label>
                    <input
                      type="text"
                      id="price"
                      name="price"
                      required
                      className="input-field mt-1"
                      value={formData.price}
                      onChange={handleInputChange}
                    />
                  </div>
                  <div>
                    <label htmlFor="currency" className="block text-sm font-medium text-gray-700">Currency</label>
                    <select
                      id="currency"
                      name="currency"
                      required
                      className="input-field mt-1"
                      value={formData.currency}
                      onChange={handleInputChange}
                    >
                      <option value="USD">USD</option>
                      <option value="EUR">EUR</option>
                      <option value="GBP">GBP</option>
                    </select>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="application_limit" className="block text-sm font-medium text-gray-700">Application Limit</label>
                    <input
                      type="number"
                      id="application_limit"
                      name="application_limit"
                      className="input-field mt-1"
                      value={formData.application_limit}
                      onChange={handleInputChange}
                    />
                  </div>
                  <div>
                    <label htmlFor="customization_limit" className="block text-sm font-medium text-gray-700">Customization Limit</label>
                    <input
                      type="number"
                      id="customization_limit"
                      name="customization_limit"
                      className="input-field mt-1"
                      value={formData.customization_limit}
                      onChange={handleInputChange}
                    />
                  </div>
                </div>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="is_active"
                    name="is_active"
                    className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
                    checked={formData.is_active}
                    onChange={handleInputChange}
                  />
                  <label htmlFor="is_active" className="ml-2 block text-sm text-gray-900">
                    Active
                  </label>
                </div>
                
                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    className="btn-secondary"
                    onClick={() => setShowModal(false)}
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="btn-primary"
                  >
                    {editingPlan ? 'Update Plan' : 'Create Plan'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Subscriptions;