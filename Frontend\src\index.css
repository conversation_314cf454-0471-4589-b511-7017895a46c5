@import "tailwindcss";

@tailwind utilities;

@layer base {
  * {
    box-sizing: border-box;
  }
  
  html {
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }
  
  body {
    margin: 0;
    padding: 0;
    background-color: #f8fafc;
    color: #1e293b;
    line-height: 1.6;
  }
}

@layer components {
  .btn-primary {
    @apply bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }
  
  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
  }
  
  .input-field {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent;
  }
  
  .sidebar-link {
    @apply flex items-center px-4 py-3 text-gray-700 hover:bg-red-50 hover:text-red-600 transition-colors duration-200 rounded-lg;
  }
  
  .sidebar-link.active {
    @apply bg-red-100 text-red-700 font-medium;
  }
}
