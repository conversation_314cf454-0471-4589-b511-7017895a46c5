from fastapi import APIRouter, Depends, HTTPException, Query, status, Path
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import desc, and_, or_, func
from datetime import datetime, timedelta, date
from typing import Optional, List
from model import User, JobApplication, Payment, UserSubscription, Plan, Resume, AIUsageLog
from database import SessionLocal
from admin import get_current_admin
from pydantic import BaseModel
from admin_schemas import (
    AdminDashboardResponse, UserManagementResponse, UserManagementUser,
    UserDetailResponse, UserResume, UserJobApplication, UserAIUsage,
    PlanCreate, PlanUpdate, PlanResponse, PlansResponse,
    PaymentResponse, PaymentsResponse
)

router = APIRouter(prefix="/admin", tags=["admin"])

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Additional Admin-specific schemas
class AdminDashboardResponse(BaseModel):
    total_users: int
    active_users: int
    inactive_users: int
    total_job_applications: int
    total_revenue: float
    total_plans: int
    active_subscriptions: int
    recent_payments: List[PaymentResponse]
    recent_users: List[UserManagementUser]

class AdminStats(BaseModel):
    total_admins: int
    total_customers: int

# Dashboard Endpoints
@router.get('/dashboard', response_model=AdminDashboardResponse)
async def get_admin_dashboard(db: Session = Depends(get_db), current_admin: User = Depends(get_current_admin)):
    # Total users
    total_users = db.query(User).count()
    
    # Active/inactive users (last_login_at within 30 days)
    thirty_days_ago = datetime.utcnow() - timedelta(days=30)
    active_users = db.query(User).filter(User.last_login_at != None, User.last_login_at >= thirty_days_ago).count()
    inactive_users = total_users - active_users
    
    # Total job applications
    total_job_applications = db.query(JobApplication).count()
    
    # Total revenue
    payments = db.query(Payment.amount).all()
    total_revenue = sum(float(p.amount) for p in payments if p.amount is not None)
    
    # Total plans and active subscriptions
    total_plans = db.query(Plan).count()
    active_subscriptions = db.query(UserSubscription).filter(UserSubscription.is_active == True).count()
    
    # Recent payments (last 5)
    recent_payments_query = db.query(Payment).options(
        joinedload(Payment.user),
        joinedload(Payment.plan)
    ).order_by(desc(Payment.created_at)).limit(5).all()
    
    recent_payments = [PaymentResponse(
        id=payment.id,
        user_id=payment.user_id,
        user_name=f"{payment.user.first_name} {payment.user.last_name}",
        user_email=payment.user.email,
        transaction_id=payment.transaction_id,
        amount=payment.amount,
        currency=payment.currency,
        status=payment.status,
        payment_method=payment.payment_method,
        plan_name=payment.plan.name if payment.plan else None,
        created_at=payment.created_at
    ) for payment in recent_payments_query]
    
    # Recent users (last 5)
    recent_users_query = db.query(User).options(
        joinedload(User.subscriptions).joinedload(UserSubscription.plan)
    ).order_by(desc(User.created_at)).limit(5).all()
    
    recent_users = []
    for user in recent_users_query:
        # Get the most recent subscription (if any)
        sub = None
        if user.subscriptions:
            sub = max(user.subscriptions, key=lambda s: s.created_at or datetime.min)
        recent_users.append(UserManagementUser(
            id=user.id,
            first_name=user.first_name,
            last_name=user.last_name,
            email=user.email,
            subscription_plan=sub.plan.name if sub and sub.plan else None,
            subscription_active=sub.is_active if sub else None,
            subscription_end_date=sub.end_date if sub else None,
            is_active=bool(user.last_login_at and user.last_login_at >= thirty_days_ago)
        ))
    
    return AdminDashboardResponse(
        total_users=total_users,
        active_users=active_users,
        inactive_users=inactive_users,
        total_job_applications=total_job_applications,
        total_revenue=total_revenue,
        total_plans=total_plans,
        active_subscriptions=active_subscriptions,
        recent_payments=recent_payments,
        recent_users=recent_users
    )

@router.get('/stats', response_model=AdminStats)
async def get_admin_stats(db: Session = Depends(get_db), current_admin: User = Depends(get_current_admin)):
    total_admins = db.query(User).filter(User.role == "admin").count()
    total_customers = db.query(User).filter(User.role == "customer").count()
    
    return AdminStats(
        total_admins=total_admins,
        total_customers=total_customers
    )

# User Management Endpoints
@router.get('/users', response_model=UserManagementResponse)
async def get_all_users(db: Session = Depends(get_db), current_admin: User = Depends(get_current_admin)):
    users = db.query(User).options(joinedload(User.subscriptions).joinedload(UserSubscription.plan)).all()
    now = datetime.utcnow()
    thirty_days_ago = now - timedelta(days=30)
    user_list = []
    for user in users:
        # Get the most recent subscription (if any)
        sub = None
        if user.subscriptions:
            sub = max(user.subscriptions, key=lambda s: s.created_at or datetime.min)
        user_list.append(UserManagementUser(
            id=user.id,
            first_name=user.first_name,
            last_name=user.last_name,
            email=user.email,
            subscription_plan=sub.plan.name if sub and sub.plan else None,
            subscription_active=sub.is_active if sub else None,
            subscription_end_date=sub.end_date if sub else None,
            is_active=bool(user.last_login_at and user.last_login_at >= thirty_days_ago)
        ))
    return UserManagementResponse(users=user_list)

@router.get('/users/{user_id}', response_model=UserDetailResponse)
async def get_user_details(user_id: int, db: Session = Depends(get_db), current_admin: User = Depends(get_current_admin)):
    user = db.query(User).options(
        joinedload(User.subscriptions).joinedload(UserSubscription.plan),
        joinedload(User.resumes),
        joinedload(User.job_applications),
        joinedload(User.ai_usage_logs)
    ).filter(User.id == user_id).first()
    
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    now = datetime.utcnow()
    thirty_days_ago = now - timedelta(days=30)
    
    # Get the most recent subscription
    sub = None
    if user.subscriptions:
        sub = max(user.subscriptions, key=lambda s: s.created_at or datetime.min)
    
    # Convert resumes
    resumes = [UserResume(
        id=resume.id,
        version_name=resume.version_name,
        is_primary=resume.is_primary,
        created_at=resume.created_at
    ) for resume in user.resumes]
    
    # Convert job applications
    job_applications = [UserJobApplication(
        id=app.id,
        job_title=app.job_title,
        company_name=app.company_name,
        application_date=app.application_date,
        status=app.status
    ) for app in user.job_applications]
    
    # Convert AI usage logs
    ai_usage_logs = [UserAIUsage(
        id=log.id,
        action_type=log.action_type,
        usage_count=log.usage_count,
        created_at=log.created_at
    ) for log in user.ai_usage_logs]
    
    return UserDetailResponse(
        id=user.id,
        first_name=user.first_name,
        last_name=user.last_name,
        email=user.email,
        subscription_plan=sub.plan.name if sub and sub.plan else None,
        subscription_active=sub.is_active if sub else None,
        subscription_end_date=sub.end_date if sub else None,
        is_active=bool(user.last_login_at and user.last_login_at >= thirty_days_ago),
        total_job_applications=len(job_applications),
        total_resumes=len(resumes),
        total_ai_usage=sum(log.usage_count for log in user.ai_usage_logs),
        job_applications=job_applications,
        resumes=resumes,
        ai_usage_logs=ai_usage_logs
    )

# Subscription Management Endpoints
@router.get('/subscriptions', response_model=PlansResponse)
async def get_all_subscription_plans(db: Session = Depends(get_db), current_admin: User = Depends(get_current_admin)):
    plans = db.query(Plan).order_by(desc(Plan.created_at)).all()
    plan_list = [PlanResponse(
        id=plan.id,
        name=plan.name,
        price=plan.price,
        currency=plan.currency,
        application_limit=plan.application_limit,
        customization_limit=plan.customization_limit,
        is_active=plan.is_active,
        created_at=plan.created_at
    ) for plan in plans]
    return PlansResponse(plans=plan_list)

@router.post('/subscriptions', response_model=PlanResponse)
async def create_subscription_plan(plan_data: PlanCreate, db: Session = Depends(get_db), current_admin: User = Depends(get_current_admin)):
    # Check if plan name already exists
    existing_plan = db.query(Plan).filter(Plan.name == plan_data.name).first()
    if existing_plan:
        raise HTTPException(status_code=400, detail="Plan name already exists")
    
    new_plan = Plan(
        name=plan_data.name,
        price=plan_data.price,
        currency=plan_data.currency,
        application_limit=plan_data.application_limit,
        customization_limit=plan_data.customization_limit
    )
    db.add(new_plan)
    db.commit()
    db.refresh(new_plan)
    
    return PlanResponse(
        id=new_plan.id,
        name=new_plan.name,
        price=new_plan.price,
        currency=new_plan.currency,
        application_limit=new_plan.application_limit,
        customization_limit=new_plan.customization_limit,
        is_active=new_plan.is_active,
        created_at=new_plan.created_at
    )

@router.put('/subscriptions/{plan_id}', response_model=PlanResponse)
async def update_subscription_plan(plan_id: int, plan_data: PlanUpdate, db: Session = Depends(get_db), current_admin: User = Depends(get_current_admin)):
    plan = db.query(Plan).filter(Plan.id == plan_id).first()
    if not plan:
        raise HTTPException(status_code=404, detail="Plan not found")
    
    # Check if new name already exists (if name is being updated)
    if plan_data.name and plan_data.name != plan.name:
        existing_plan = db.query(Plan).filter(Plan.name == plan_data.name).first()
        if existing_plan:
            raise HTTPException(status_code=400, detail="Plan name already exists")
    
    # Update fields
    for field, value in plan_data.dict(exclude_unset=True).items():
        setattr(plan, field, value)
    
    plan.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(plan)
    
    return PlanResponse(
        id=plan.id,
        name=plan.name,
        price=plan.price,
        currency=plan.currency,
        application_limit=plan.application_limit,
        customization_limit=plan.customization_limit,
        is_active=plan.is_active,
        created_at=plan.created_at
    )

@router.delete('/subscriptions/{plan_id}')
async def delete_subscription_plan(plan_id: int, db: Session = Depends(get_db), current_admin: User = Depends(get_current_admin)):
    plan = db.query(Plan).filter(Plan.id == plan_id).first()
    if not plan:
        raise HTTPException(status_code=404, detail="Plan not found")
    
    # Check if plan has active subscriptions
    active_subscriptions = db.query(UserSubscription).filter(
        and_(UserSubscription.plan_id == plan_id, UserSubscription.is_active == True)
    ).first()
    
    if active_subscriptions:
        raise HTTPException(status_code=400, detail="Cannot delete plan with active subscriptions")
    
    db.delete(plan)
    db.commit()
    return {"message": "Plan deleted successfully"}

# Payment Management Endpoints
@router.get('/payments', response_model=PaymentsResponse)
async def get_all_payments(
    db: Session = Depends(get_db),
    current_admin: User = Depends(get_current_admin),
    start_date: Optional[date] = Query(None, description="Filter from date (YYYY-MM-DD)"),
    end_date: Optional[date] = Query(None, description="Filter to date (YYYY-MM-DD)"),
    plan_id: Optional[int] = Query(None, description="Filter by plan ID"),
    min_amount: Optional[float] = Query(None, description="Minimum amount filter"),
    max_amount: Optional[float] = Query(None, description="Maximum amount filter"),
    status: Optional[str] = Query(None, description="Payment status filter"),
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(50, ge=1, le=100, description="Items per page")
):
    query = db.query(Payment).options(
        joinedload(Payment.user),
        joinedload(Payment.plan)
    )
    
    # Apply filters
    if start_date:
        start_datetime = datetime.combine(start_date, datetime.min.time())
        query = query.filter(Payment.created_at >= start_datetime)
    
    if end_date:
        end_datetime = datetime.combine(end_date, datetime.max.time())
        query = query.filter(Payment.created_at <= end_datetime)
    
    if plan_id:
        query = query.filter(Payment.plan_id == plan_id)
    
    if min_amount is not None:
        query = query.filter(func.cast(Payment.amount, float) >= min_amount)
    
    if max_amount is not None:
        query = query.filter(func.cast(Payment.amount, float) <= max_amount)
    
    if status:
        query = query.filter(Payment.status.ilike(f"%{status}%"))
    
    # Get total count before pagination
    total_count = query.count()
    
    # Apply pagination
    offset = (page - 1) * limit
    payments = query.order_by(desc(Payment.created_at)).offset(offset).limit(limit).all()
    
    payment_list = [PaymentResponse(
        id=payment.id,
        user_id=payment.user_id,
        user_name=f"{payment.user.first_name} {payment.user.last_name}",
        user_email=payment.user.email,
        transaction_id=payment.transaction_id,
        amount=payment.amount,
        currency=payment.currency,
        status=payment.status,
        payment_method=payment.payment_method,
        plan_name=payment.plan.name if payment.plan else None,
        created_at=payment.created_at
    ) for payment in payments]
    
    return PaymentsResponse(payments=payment_list, total_count=total_count)

# Additional Admin-specific endpoints

# User Activity Analytics
class UserActivityResponse(BaseModel):
    total_logins: int
    average_logins_per_user: float
    most_active_day: str
    least_active_day: str

@router.get('/analytics/user-activity', response_model=UserActivityResponse)
async def get_user_activity_analytics(db: Session = Depends(get_db), current_admin: User = Depends(get_current_admin)):
    # This is a placeholder implementation since we don't have login history table
    # In a real implementation, you would query a login_history table
    
    # For demonstration purposes, we'll use last_login_at as a proxy
    total_logins = db.query(User).filter(User.last_login_at != None).count()
    total_users = db.query(User).count()
    average_logins = total_logins / total_users if total_users > 0 else 0
    
    # Placeholder for most/least active days
    # In a real implementation, you would aggregate login data by day
    most_active_day = "Monday"
    least_active_day = "Sunday"
    
    return UserActivityResponse(
        total_logins=total_logins,
        average_logins_per_user=average_logins,
        most_active_day=most_active_day,
        least_active_day=least_active_day
    )

# Revenue Analytics
class RevenueAnalyticsResponse(BaseModel):
    total_revenue: float
    average_revenue_per_user: float
    most_popular_plan: str
    highest_revenue_day: str

@router.get('/analytics/revenue', response_model=RevenueAnalyticsResponse)
async def get_revenue_analytics(db: Session = Depends(get_db), current_admin: User = Depends(get_current_admin)):
    # Calculate total revenue
    payments = db.query(Payment.amount).all()
    total_revenue = sum(float(p.amount) for p in payments if p.amount is not None)
    
    # Calculate average revenue per user
    total_users = db.query(User).count()
    average_revenue_per_user = total_revenue / total_users if total_users > 0 else 0
    
    # Find most popular plan
    popular_plan_query = db.query(
        Plan.name, func.count(UserSubscription.id).label('subscription_count')
    ).join(UserSubscription).group_by(Plan.name).order_by(desc('subscription_count')).first()
    
    most_popular_plan = popular_plan_query[0] if popular_plan_query else "No subscriptions"
    
    # Placeholder for highest revenue day
    # In a real implementation, you would aggregate payment data by day
    highest_revenue_day = "Friday"
    
    return RevenueAnalyticsResponse(
        total_revenue=total_revenue,
        average_revenue_per_user=average_revenue_per_user,
        most_popular_plan=most_popular_plan,
        highest_revenue_day=highest_revenue_day
    )