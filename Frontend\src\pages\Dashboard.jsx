import React, { useState, useEffect } from 'react';
import { Users, DollarSign, FileText, CreditCard, TrendingUp, TrendingDown } from 'lucide-react';
import axios from 'axios';

const StatCard = ({ title, value, icon: Icon, change, changeType }) => {
  return (
    <div className="card">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          {change && (
            <div className={`flex items-center mt-2 text-sm ${
              changeType === 'increase' ? 'text-green-600' : 'text-red-600'
            }`}>
              {changeType === 'increase' ? (
                <TrendingUp className="w-4 h-4 mr-1" />
              ) : (
                <TrendingDown className="w-4 h-4 mr-1" />
              )}
              {change}
            </div>
          )}
        </div>
        <div className={`p-3 rounded-full ${
          title === 'Total Users' ? 'bg-blue-100' :
          title === 'Total Revenue' ? 'bg-green-100' :
          title === 'Job Applications' ? 'bg-purple-100' :
          'bg-orange-100'
        }`}>
          <Icon className={`w-6 h-6 ${
            title === 'Total Users' ? 'text-blue-600' :
            title === 'Total Revenue' ? 'text-green-600' :
            title === 'Job Applications' ? 'text-purple-600' :
            'text-orange-600'
          }`} />
        </div>
      </div>
    </div>
  );
};

const RecentActivity = ({ title, items, type }) => {
  return (
    <div className="card">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
      <div className="space-y-3">
        {items.length > 0 ? (
          items.map((item, index) => (
            <div key={index} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
              <div className="flex-1">
                {type === 'users' ? (
                  <>
                    <p className="font-medium text-gray-900">{item.first_name} {item.last_name}</p>
                    <p className="text-sm text-gray-500">{item.email}</p>
                    {item.subscription_plan && (
                      <span className="inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full mt-1">
                        {item.subscription_plan}
                      </span>
                    )}
                  </>
                ) : (
                  <>
                    <p className="font-medium text-gray-900">{item.user_name}</p>
                    <p className="text-sm text-gray-500">${item.amount} - {item.status}</p>
                    {item.plan_name && (
                      <span className="inline-block px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full mt-1">
                        {item.plan_name}
                      </span>
                    )}
                  </>
                )}
              </div>
              <div className="text-sm text-gray-500">
                {new Date(type === 'users' ? item.created_at : item.created_at).toLocaleDateString()}
              </div>
            </div>
          ))
        ) : (
          <p className="text-gray-500 text-center py-4">No recent {type} found</p>
        )}
      </div>
    </div>
  );
};

const Dashboard = () => {
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const response = await axios.get('/admin/dashboard');
      setDashboardData(response.data);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      setError('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <p className="text-red-800">{error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <button 
          onClick={fetchDashboardData}
          className="btn-secondary"
        >
          Refresh
        </button>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Users"
          value={dashboardData?.total_users || 0}
          icon={Users}
          change="+12% from last month"
          changeType="increase"
        />
        <StatCard
          title="Total Revenue"
          value={`$${dashboardData?.total_revenue?.toFixed(2) || '0.00'}`}
          icon={DollarSign}
          change="+8% from last month"
          changeType="increase"
        />
        <StatCard
          title="Job Applications"
          value={dashboardData?.total_job_applications || 0}
          icon={FileText}
          change="+15% from last month"
          changeType="increase"
        />
        <StatCard
          title="Active Subscriptions"
          value={dashboardData?.active_subscriptions || 0}
          icon={CreditCard}
          change="+5% from last month"
          changeType="increase"
        />
      </div>

      {/* Activity Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">User Activity Overview</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Active Users (30 days)</span>
              <span className="font-semibold text-green-600">{dashboardData?.active_users || 0}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Inactive Users</span>
              <span className="font-semibold text-red-600">{dashboardData?.inactive_users || 0}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Total Plans</span>
              <span className="font-semibold text-blue-600">{dashboardData?.total_plans || 0}</span>
            </div>
          </div>
        </div>

        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="space-y-3">
            <button className="w-full btn-primary text-left">
              Create New Subscription Plan
            </button>
            <button className="w-full btn-secondary text-left">
              Export User Data
            </button>
            <button className="w-full btn-secondary text-left">
              View Analytics Report
            </button>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <RecentActivity
          title="Recent Users"
          items={dashboardData?.recent_users || []}
          type="users"
        />
        <RecentActivity
          title="Recent Payments"
          items={dashboardData?.recent_payments || []}
          type="payments"
        />
      </div>
    </div>
  );
};

export default Dashboard;