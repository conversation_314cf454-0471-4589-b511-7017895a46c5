from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import admin
import admin_endpoints
from database import Base, engine

app = FastAPI(title="Admin Panel API", description="API for admin panel management", version="1.0.0")

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173"],  # Frontend URL
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create tables in the database
if __name__ == '__main__':
    Base.metadata.create_all(engine)


# Include admin routers only
app.include_router(admin.router)
app.include_router(admin_endpoints.router)

