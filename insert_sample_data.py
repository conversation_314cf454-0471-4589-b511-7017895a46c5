from sqlalchemy.orm import Session
from database import SessionLocal, engine
from model import User, Plan, UserSubscription, Payment, Resume, JobApplication, AIUsageLog, Base
from datetime import datetime, date, timedelta
from passlib.context import CryptContext
import random

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def get_password_hash(password):
    return pwd_context.hash(password)

def insert_sample_data():
    # Create tables if they don't exist
    Base.metadata.create_all(bind=engine)
    
    db = SessionLocal()
    try:
        # Check if data already exists
        if db.query(User).count() > 0:
            print("Sample data already exists. Skipping insertion.")
            return
        
        # Create admin user
        admin_user = User(
            first_name="Admin",
            last_name="User",
            email="<EMAIL>",
            password=get_password_hash("admin123"),
            role="admin",
            last_login_at=datetime.utcnow() - timedelta(days=1)
        )
        db.add(admin_user)
        
        # Create sample plans
        plans = [
            Plan(
                name="Basic Plan",
                price="9.99",
                currency="USD",
                application_limit=10,
                customization_limit=5,
                is_active=True
            ),
            Plan(
                name="Premium Plan",
                price="19.99",
                currency="USD",
                application_limit=50,
                customization_limit=20,
                is_active=True
            ),
            Plan(
                name="Enterprise Plan",
                price="49.99",
                currency="USD",
                application_limit=None,
                customization_limit=None,
                is_active=True
            )
        ]
        
        for plan in plans:
            db.add(plan)
        
        db.commit()
        
        # Create sample users
        sample_users = [
            {
                "first_name": "John",
                "last_name": "Doe",
                "email": "<EMAIL>",
                "password": get_password_hash("password123"),
                "role": "customer",
                "last_login_at": datetime.utcnow() - timedelta(days=2)
            },
            {
                "first_name": "Jane",
                "last_name": "Smith",
                "email": "<EMAIL>",
                "password": get_password_hash("password123"),
                "role": "customer",
                "last_login_at": datetime.utcnow() - timedelta(days=5)
            },
            {
                "first_name": "Mike",
                "last_name": "Johnson",
                "email": "<EMAIL>",
                "password": get_password_hash("password123"),
                "role": "customer",
                "last_login_at": datetime.utcnow() - timedelta(days=1)
            },
            {
                "first_name": "Sarah",
                "last_name": "Wilson",
                "email": "<EMAIL>",
                "password": get_password_hash("password123"),
                "role": "customer",
                "last_login_at": datetime.utcnow() - timedelta(days=45)  # Inactive user
            }
        ]
        
        users = []
        for user_data in sample_users:
            user = User(**user_data)
            db.add(user)
            users.append(user)
        
        db.commit()
        
        # Create sample subscriptions
        for i, user in enumerate(users):
            if i < 3:  # First 3 users have subscriptions
                plan = plans[i % len(plans)]
                subscription = UserSubscription(
                    user_id=user.id,
                    plan_id=plan.id,
                    start_date=date.today() - timedelta(days=30),
                    end_date=date.today() + timedelta(days=30),
                    is_active=True
                )
                db.add(subscription)
        
        db.commit()
        
        # Create sample payments
        payment_data = [
            {
                "user_id": users[0].id,
                "transaction_id": "TXN001",
                "amount": "9.99",
                "currency": "USD",
                "status": "completed",
                "payment_method": "credit_card",
                "plan_id": plans[0].id,
                "created_at": datetime.utcnow() - timedelta(days=30)
            },
            {
                "user_id": users[1].id,
                "transaction_id": "TXN002",
                "amount": "19.99",
                "currency": "USD",
                "status": "completed",
                "payment_method": "paypal",
                "plan_id": plans[1].id,
                "created_at": datetime.utcnow() - timedelta(days=25)
            },
            {
                "user_id": users[2].id,
                "transaction_id": "TXN003",
                "amount": "49.99",
                "currency": "USD",
                "status": "completed",
                "payment_method": "credit_card",
                "plan_id": plans[2].id,
                "created_at": datetime.utcnow() - timedelta(days=20)
            },
            {
                "user_id": users[0].id,
                "transaction_id": "TXN004",
                "amount": "9.99",
                "currency": "USD",
                "status": "pending",
                "payment_method": "credit_card",
                "plan_id": plans[0].id,
                "created_at": datetime.utcnow() - timedelta(days=5)
            }
        ]
        
        for payment in payment_data:
            db.add(Payment(**payment))
        
        # Create sample resumes
        for i, user in enumerate(users[:3]):
            resume = Resume(
                user_id=user.id,
                resume_data=f"Sample resume data for {user.first_name} {user.last_name}",
                version_name=f"Resume v{i+1}",
                is_primary=True,
                parsed_summary_json='{"skills": ["Python", "JavaScript"], "experience": "3 years"}'
            )
            db.add(resume)
        
        db.commit()  # Commit resumes first
        
        # Get the created resumes
        created_resumes = db.query(Resume).all()
        
        # Create sample job applications
        job_applications_data = [
            {"user_id": users[0].id, "resume_id": created_resumes[0].id, "job_title": "Software Engineer", "company_name": "Tech Corp", "application_date": date.today() - timedelta(days=10), "status": "applied"},
            {"user_id": users[0].id, "resume_id": created_resumes[0].id, "job_title": "Frontend Developer", "company_name": "Web Solutions", "application_date": date.today() - timedelta(days=8), "status": "interview"},
            {"user_id": users[1].id, "resume_id": created_resumes[1].id, "job_title": "Data Scientist", "company_name": "Data Inc", "application_date": date.today() - timedelta(days=15), "status": "rejected"},
            {"user_id": users[1].id, "resume_id": created_resumes[1].id, "job_title": "ML Engineer", "company_name": "AI Startup", "application_date": date.today() - timedelta(days=5), "status": "applied"},
            {"user_id": users[2].id, "resume_id": created_resumes[2].id, "job_title": "DevOps Engineer", "company_name": "Cloud Services", "application_date": date.today() - timedelta(days=12), "status": "hired"}
        ]
        
        for app_data in job_applications_data:
            db.add(JobApplication(**app_data))
        
        # Create sample AI usage logs
        ai_usage_data = [
            {"user_id": users[0].id, "action_type": "resume_optimization", "usage_count": 5, "created_at": datetime.utcnow() - timedelta(days=7)},
            {"user_id": users[0].id, "action_type": "cover_letter_generation", "usage_count": 3, "created_at": datetime.utcnow() - timedelta(days=5)},
            {"user_id": users[1].id, "action_type": "job_matching", "usage_count": 10, "created_at": datetime.utcnow() - timedelta(days=10)},
            {"user_id": users[1].id, "action_type": "interview_prep", "usage_count": 2, "created_at": datetime.utcnow() - timedelta(days=3)},
            {"user_id": users[2].id, "action_type": "resume_optimization", "usage_count": 8, "created_at": datetime.utcnow() - timedelta(days=6)}
        ]
        
        for usage_data in ai_usage_data:
            db.add(AIUsageLog(**usage_data))
        
        db.commit()
        
        print("Sample data inserted successfully!")
        print("\nAdmin Login Credentials:")
        print("Email: <EMAIL>")
        print("Password: admin123")
        print("\nSample Users:")
        for user_data in sample_users:
            print(f"Email: {user_data['email']}, Password: password123")
        
    except Exception as e:
        print(f"Error inserting sample data: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    insert_sample_data()